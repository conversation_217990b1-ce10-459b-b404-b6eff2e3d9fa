import "umi/typings";

// Baidu Map types
declare global {
  interface Window {
    BMAP_AUTHENTIC_KEY: string;

    BMAP_SATELLITE_MAP: BMapGL.MapType;
  }

  namespace BMapSub {
    class Subway {
      constructor(containerId: string, cityCode: string);
      // Add other methods you might use
      show(): void;
      hide(): void;
      destroy(): void;
      setZoom(zoom: number): void;
      addEventListener(event: string, callback: () => void): void;
    }

    class Direction {
      constructor(subway: Subway);
      search(start: string, end: string): void;
    }
  }

  const BMapSub: typeof BMapSub;

  interface SubwayInstance {
    event: {
      on(eventName: string, callback: () => void): void;
    };
    showLine(lineName: string): void;
    clearLine(): void;
    setFitView(element: Element): number;
    scale(value: number): void;
    getLineList(callback: (list: Page.SubwayLineItem[]) => void): void;
  }

  interface SubwayOptions {
    /**
     * 表示是否开启简易模式,true表示开启，false表示不开启。默认为false，即：不开启。
     */
    easy?: boolean;
    /**
     * 表示默认展示的地铁图城市adcode，默认为1100（北京），adcode可以通过getCityList方法获取。
     */
    adcode?: string;
    /**
     * 主题，目前支持两种主题模式，'normal'（标准）和'colorful'（站点颜色跟随线路颜色），默认为'normal'。
     */
    theme?: 'normal' | 'colorful';
    /**
     * 地铁图的高度
     */
    height?: number;
    /**
     * 地铁图的宽度
     */
    width?: number;
    /**
     * 表示是否开启双击放大模式，{switch: false} 表示不开启，{switch: true}表示开启。默认为不开启。
     */
    doubleclick?: {
      switch: boolean;
    };
  }

  const subway: (containerId: string, options?: SubwayOptions) => SubwayInstance;
}


