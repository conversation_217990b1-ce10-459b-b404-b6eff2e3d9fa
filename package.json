{"private": true, "author": "王明远 <wangmingyuan>", "scripts": {"dev": "umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@pansy/china-division": "^2.1.0", "@tailwindcss/postcss": "^4.1.11", "@vant/area-data": "^2.0.0", "ahooks": "^3.9.0", "antd": "^5.26.5", "clsx": "^2.1.1", "dayjs": "^1.11.13", "echarts": "^6.0.0", "echarts-for-react": "^3.0.2", "es-toolkit": "^1.39.9", "nanoid": "^5.1.5", "react-bmapgl": "^1.0.1", "react-custom-scrollbars": "^4.2.1", "react-if": "^4.1.6", "react-map-interaction": "^2.1.0", "tailwindcss": "^4.1.11", "umi": "^4.4.11", "zustand": "^5.0.6"}, "devDependencies": {"@types/bmapgl": "^0.0.7", "@types/react": "^18.0.33", "@types/react-custom-scrollbars": "^4.0.13", "@types/react-dom": "^18.0.11", "@umijs/lint": "^4.4.11", "@umijs/plugins": "^4.4.11", "eslint": "^8.57.1", "stylelint": "^14", "tailwindcss": "^3", "typescript": "^5.0.3"}}