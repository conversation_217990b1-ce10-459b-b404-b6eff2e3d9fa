import { defineConfig } from "umi";

const isDev = process.env.NODE_ENV === "development";

export default defineConfig({
  plugins: ["@umijs/plugins/dist/tailwindcss", "@umijs/plugins/dist/request"],
  base: isDev ? undefined : "/",
  publicPath: isDev ? '/screen/' : "/screen/",
  routes: [
    // {
    //   path: "/",
    //   redirect: "/userManage",
    // },
    { path: "/", component: "Home" },
    { path: "/test", component: "Test" }
  ],

  monorepoRedirect: {},
  request: {
    dataField: "data",
  },
  proxy: {
    "/api": {
      target: "http://*************:19120",
      changeOrigin: true,
      // 支持https
      // secure: true,
      pathRewrite: { "^/api": "" },
    },
  },
  headScripts: [
    // "https://gis.10010.com:8219/dugis-tn/dugis-demo-3d/api/booter.js"
    // "https://webapi.amap.com/subway/main?v=1.0&version=1.0.13",
    "/screen/subway/index.js",
    // "https://api.map.baidu.com/api?type=subway&v=1.0&ak=1XjLLEhZhQNUzd93EjU5nOGQ",
    // "https://gis.10010.com:8219/dugis-tn/dugis-demo-3d/api/booter.js",
    "https://api.map.baidu.com/api?type=webgl&v=1.0&ak=1XjLLEhZhQNUzd93EjU5nOGQ",
  ],
  metas: [{
    name: "viewport",
    content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0,shrink-to-fit=no"
  }],
  history: { type: "hash" },
  npmClient: "pnpm",
  // tailwindcss: {},
  extraPostCSSPlugins: [
    require("@tailwindcss/postcss"),
    // require("./postcss-arbitrary-scale")({
    //   scaleFactor: 4, // 所有数值除以4
    // }),
  ],
});
