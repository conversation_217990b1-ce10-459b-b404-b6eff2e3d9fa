// postcss-arbitrary-scale.js
const plugin = require('postcss').plugin;

module.exports = plugin('postcss-arbitrary-scale', (opts = {}) => {
  const scaleFactor = opts.scaleFactor || 1; // 缩放因子

  return (root) => {
    root.walkRules(rule => {
      // 只处理包含方括号的 Tailwind 任意值类
      if (rule.selector.includes('[') && rule.selector.includes(']')) {
        rule.walkDecls(decl => {
          // 匹配带单位的数值 (px, rem, em, % 等)
          decl.value = decl.value.replace(
            /(\d*\.?\d+)(px|rem|em|%|vw|vh|vmin|vmax)/g,
            (_, num, unit) => (parseFloat(num) / scaleFactor) + unit
          );
        });
      }
    });
  };
});