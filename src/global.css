@import "antd/dist/reset.css";
@import "tailwindcss";
@import "../public/font.css";


.value-font{
    font-family: DINCond-Black;
}

body{
    color: #FFFF;
    font-family: "HYQiHei-75W";
}

.centered {
    @apply absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2;
}
  
.x-centered {
    @apply absolute left-1/2 -translate-x-1/2;
}
  
.y-centered {
    @apply absolute top-1/2 -translate-y-1/2;
}

.anchorBL{
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* #highSpeedTrainMap #mask{
    opacity: 1 !important;
    background: rgba(0, 0, 0, 20%) !important;
} */

/* stylelint-disable-next-line selector-id-pattern */
#container #sw_renderer{
    display: flex;
    justify-content: center;
    align-items: center;
}

#subway-svg{
    left: 0 !important;
    top: 0 !important;
    fill:rgba(255,255,255,85%);
}

.amap-subway-api #select_bg{
    fill:#202E40 !important;
}

#subway-box  > div > div > div {
    transform-origin: center center !important;
}


/* 百度地铁线路图 */

/* #subway-container svg>g{
    transform:none !important; 
}

#subway-container svg>g rect{
    fill: #10202C !important;
}

#subway-container #sw_svg{
    @apply absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2;
} */