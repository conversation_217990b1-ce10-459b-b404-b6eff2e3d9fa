declare namespace API {
  interface PageParams {
    size: number;
    page: number;
  }

  interface Result<T> {
    data: T;
  }

  type Success = null;

  // 与后端约定的响应数据格式
  interface Response {
    code: string | number;
    msg: string;
    data: any;
  }
}

declare namespace FormConfig {
  type Paths = (string | number)[] | string | number;
}

declare namespace Page {
  type BoundaryData = {
    boundaries: BMapGL.Point[][];
  };

  type NameItemType = {
    name: string;
    centroid: [number, number];
    adcode: number;
  }

  type ListItem = {
    /**
     * 名称
     */
    name: string;

    /**
     * 唯一标识
     */
    key: string;

    /**
     * 数值
     */
    value: number;
  }

  // 地铁线路列表项
  type SubwayLineItem = {
    color: string;

    id: string;

    name: string;

    shortname: string;

    status: "1"
  }
}

declare module 'react-map-interaction' {
  export type Translation = {
    x: number;
    y: number;
  };

  export type ScaleTranslation = {
    scale: number;
    translation: Translation;
  };

  export type MapInteractionProps = {
    children?: (scaleTranslation: ScaleTranslation) => React.ReactNode;

    value?: ScaleTranslation;

    defaultValue?: ScaleTranslation;

    disableZoom?: boolean;

    disablePan?: boolean;

    translationBounds?: {
      xMin?: number;
      xMax?: number;
      yMin?: number;
      yMax?: number;
    };

    onChange?: (scaleTranslation: ScaleTranslation) => void;

    minScale?: number;
    maxScale?: number;

    showControls?: boolean;

    plusBtnContents?: React.ReactNode;
    minusBtnContents?: React.ReactNode;

    controlsClass?: string;

    btnClass?: string;

    plusBtnClass?: string;
    minusBtnClass?: string;
  };
  export const MapInteraction: React.FC<MapInteractionProps>;

  export type MapInteractionCSSProps = React.PropsWithChildren<Omit<MapInteractionProps, 'children'>>;
  export const MapInteractionCSS: React.FC<MapInteractionCSSProps>;

  export default MapInteraction;
}
