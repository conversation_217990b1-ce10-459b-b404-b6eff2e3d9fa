import { immer } from "zustand/middleware/immer";
import { shallow } from "zustand/shallow";
import { createWithEqualityFn } from "zustand/traditional";
import { AbilityEnum, SceneTypeEnum } from "@/enum";

interface GlobalState {
  token: string;
  selectCode: number;
  mapRefState?: BMapGL.Map;
  /**
   * 是否开启地图可拖拽缩放
   */
  enableDragging: boolean;
  /**
   * 选中的能力id
   */
  selectAbilityType: AbilityEnum;
  /**
   * 页面初始化加载需要地图加载完成再渲染出来
   */
  loading: boolean;
  /**
   * 地铁线路图缩放初始值
   */
  subwayScaleValue: number;
  /**
   * 场景类型
   */
  sceneType?: SceneTypeEnum;
  /**
   * 地铁线路列表
   */
  subwayLineList: Page.SubwayLineItem[]
  /**
   * 选择的线路id
   */
  selectLineId?: string;
}

const useStore = createWithEqualityFn<GlobalState>()(
  immer((set, get) => ({
    token: "",
    selectCode: 100000,
    mapRefState: undefined,
    enableDragging: true,
    selectAbilityType: AbilityEnum.UserPriorityScheduling,
    loading: true,
    subwayRefState: undefined,
    subwayScaleValue: 1.1,
    sceneType: undefined,
    subwayLineList: [],
    selectLineId: undefined
  })),
  shallow
);

export default useStore;
