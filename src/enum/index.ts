/**
 * @description: 菜单类型枚举
 *
 */
export enum MenuItemTypeEnum {
  Menu = 0,
  Button = 1,
}

/**
 * 无线策略类型
 */
export enum AbilityEnum {
  /**
   * 用户优先级调度
   */
  UserPriorityScheduling = "1",

  /**
   * 业务带宽保障
   */
  BusinessBandwidthGuarantee = "2",

  /**
   * 用户动态限速
   */
  UserDynamicSpeedLimit = "3",
}

/**
 * 省市区编码类型
 */
export enum EncodingEnum {
  /**
   * 全中国
   */
  China = "1",

  /**
   * 省份
   */
  Province = "2",

  /**
   * 市
   */
  City = "3",

  /**
   * 区
   */
  District = "4",

  /**
   * 直辖市
   */
  MeCity = "5",
}

/**
 * 场景类型
 */
export enum SceneTypeEnum {
  /**
   * 行政区
   */
  District = "1",

  /**
   * 地铁
   */
  Subway = "2",

  /**
   * 高铁站
   */
  HighSpeedTrainStation = "3",

  /**
   * 住宅小区
   */
  ResidentialCommunity = "4",

  /**
   * 重点交通干线
   */
  KeyTransportation = "5",

  /**
   * 交通枢纽
   */
  TransportationHubs = "6",

  /**
   * 重点商超
   */
  KeyShopping = "7",

  /**
   * 商务楼宇
   */
  BusinessBuilding = "8",

  /**
   * 政务中心
   */
  GovernmentCenter = "9",

  /**
   * 高等学校
   */
  HighSchool = "10",

  /**
   * 文旅景区
   */
  CultureAndTourism = "11",

  /**
   * 医疗机构
   */
  MedicalInstitution = "12",
}

