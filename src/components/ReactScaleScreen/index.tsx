import { useMemo, useRef } from "react";
import { useDebounce, useSize } from "ahooks";

export interface ReactScaleScreenProps {
  /**
   * 大屏宽度
   * @default 1920
   */
  width?: number;

  /**
   * 大屏高度
   * @default 1080
   */
  height?: number;

  /**
   * className
   */

  className?: string;

  /**
   * 适配方式
   * @default auto
   */
  scaleType?: "auto" | "full" | "width" | "height";

  delay?: number;

  style?: React.CSSProperties;

  wrapperStyle?: React.CSSProperties;
}
function ReactScaleScreen(
  props: React.PropsWithChildren<ReactScaleScreenProps>
) {
  const {
    width = 1920,
    height = 1080,
    className = "",
    scaleType = "auto",
    delay = 0,
    style,
    children,
    wrapperStyle,
  } = props;

  const panelRef = useRef<HTMLDivElement>(null);

  const size = useSize(panelRef);

  const sizeValue = useDebounce(size, { wait: delay });

  const { width: sizeWidth = 0, height: sizeHeight = 0 } = sizeValue ?? {};

  const widthScale = sizeWidth / width;
  const heightScale = sizeHeight / height;

  const fullStyleValue: React.CSSProperties = useMemo(() => {
    return {
      transform: `scale(${widthScale},${heightScale})`,
      top: 0,
      left: 0,
    };
  }, [heightScale, widthScale]);

  const autoStyleValue: React.CSSProperties = useMemo(() => {
    const scale = Math.min(widthScale, heightScale);
    const marginLeft = Math.max((sizeWidth - scale * width) / 2, 0);
    const marginTop = Math.max((sizeHeight - scale * height) / 2, 0);
    return {
      transform: `scale(${scale})`,
      top: marginTop,
      left: marginLeft,
    };
  }, [height, heightScale, sizeHeight, sizeWidth, width, widthScale]);

  const widthStyleValue: React.CSSProperties = useMemo(() => {
    const left = Math.max((sizeWidth - widthScale * width) / 2);
    return {
      transform: `scale(${widthScale})`,
      top: 0,
      left,
    };
  }, [widthScale, sizeWidth, width]);

  const heightStyleValue: React.CSSProperties = useMemo(() => {
    const top = Math.max((sizeHeight - heightScale * height) / 2);

    return {
      transform: `scale(${heightScale})`,
      top,
      left: 0,
    };
  }, [height, heightScale, sizeHeight]);

  const styleValue: React.CSSProperties = useMemo(() => {
    switch (scaleType) {
      case "auto":
        return autoStyleValue;

      case "full":
        return fullStyleValue;

      case "width":
        return widthStyleValue;

      case "height":
        return heightStyleValue;

      default:
        return {};
    }
  }, [
    autoStyleValue,
    fullStyleValue,
    heightStyleValue,
    scaleType,
    widthStyleValue,
  ]);

  return (
    <div
      className={`w-screen h-screen relative flex items-center justify-center ${className}`}
      ref={panelRef}
      style={{
        ...style,
        overflowX: scaleType === "height" ? "auto" : "hidden",
        overflowY: scaleType === "width" ? "auto" : "hidden",
      }}
    >
      <div
        className="absolute overflow-visible"
        style={{
          transformOrigin: "left top",
          width,
          height,
          ...styleValue,
          ...wrapperStyle,
        }}
      >
        {children}
        {/* {size && children} */}
      </div>
    </div>
  );
}
export default ReactScaleScreen;
