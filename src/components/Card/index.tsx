interface IProps {
  title?: string;
  className?: React.HTMLAttributes<HTMLDivElement>["className"];
  children?: React.ReactNode;
}
function Card(props: IProps) {
  const { title, className, children } = props;
  return (
    <div className={`shadow-[0px_6px_10px_0px_rgba(0,0,0,0.06)] ${className}`}>
      <div className="h-[118px] bg-[#324355] shadow-[0px_6px_10px_0px_rgba(0,0,0,0.06)] flex items-center relative z-10">
        <span className="bg-[#5FEAFF] w-[16px] h-full"></span>
        <span className="pl-[60px] truncate text-[56px]">{title}</span>
      </div>
      {/* backdrop-blur-[30px] */}
      <div className="bg-[rgba(42,65,96,1)] h-fit">{children}</div>
    </div>
  );
}
export default Card;
