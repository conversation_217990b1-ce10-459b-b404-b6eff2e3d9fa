import React, { useState, useCallback, useMemo } from "react";
import { PlusSquareOutlined, MinusSquareOutlined } from "@ant-design/icons";

// 定义树节点数据类型
interface TreeNode {
  title: string;
  key: string | number;
  children?: TreeNode[];
}

// 定义组件属性类型
interface CustomTreeProps {
  treeData: TreeNode[];
  onSelect?: (
    selectedKey: string | null | number,
    selectedNode: TreeNode | null
  ) => void;
  selectedKey?: string | null | number;
}

// 单个树节点组件
const TreeNodeComponent = React.memo<{
  node: TreeNode;
  level: number;
  expandedKeys: string[];
  selectedKey: string | number | null;
  onToggle: (key: string) => void;
  onSelect: (key: string, node: TreeNode) => void;
}>(({ node, level, expandedKeys, selectedKey, onToggle, onSelect }) => {
  const hasChildren = node.children && node.children.length > 0;
  const expanded = expandedKeys.includes(String(node.key));
  const selected = String(selectedKey) === String(node.key);

  const itemHeight = 82;

  const indentStyle = {
    paddingLeft: `${level * 20 + 28}px`,
    height: itemHeight,
  };

  const handleToggleClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      if (hasChildren) {
        onToggle(String(node.key));
      }
    },
    [hasChildren, node.key, onToggle]
  );

  const handleNodeClick = useCallback(() => {
    onSelect(String(node.key), node);
  }, [node, onSelect]);

  return (
    <>
      <div
        className={`
          flex items-center rounded-[8px] cursor-pointer relative overflow-hidden
          transition-all duration-200 ease-in-out
          ${selected ? "bg-[#384E67]" : "hover:bg-[rgba(255,255,255,0.05)]"}
        `}
        style={indentStyle}
        onClick={handleNodeClick}
      >
        {/* 展开/收起图标 */}
        <div
          className="size-[48px] text-[32px] text-[rgba(255,255,255,0.6)] mr-3 flex items-center justify-center"
          onClick={handleToggleClick}
        >
          {hasChildren ? (
            expanded ? (
              <MinusSquareOutlined />
            ) : (
              <PlusSquareOutlined />
            )
          ) : (
            <span className="size-[48px]" /> // 占位符，保持对齐
          )}
        </div>

        {/* 节点标题 */}
        <span
          className={`
          flex-1 text-[32px] transition-colors duration-200
          ${selected ? "text-white" : "text-[rgba(255,255,255,0.85)]"}
        `}
        >
          {node.title}
        </span>
      </div>

      {/* 子节点容器 - 使用 CSS transition 实现动画 */}
      {hasChildren && (
        <div
          className="overflow-hidden transition-all duration-300 ease-in-out"
          style={{
            maxHeight: expanded
              ? `${(node.children?.length || 0) * itemHeight}px`
              : "0px",
            opacity: expanded ? 1 : 0,
          }}
        >
          {node.children?.map((child) => (
            <TreeNodeComponent
              key={child.key}
              node={child}
              level={level + 1}
              expandedKeys={expandedKeys}
              selectedKey={selectedKey}
              onToggle={onToggle}
              onSelect={onSelect}
            />
          ))}
        </div>
      )}
    </>
  );
});

// 主树组件
const CustomTree: React.FC<CustomTreeProps> = ({
  treeData,
  onSelect,
  selectedKey = null,
}) => {
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [internalSelectedKey, setInternalSelectedKey] = useState<
    string | number | null
  >(selectedKey);

  // 使用 useMemo 优化渲染性能
  const currentSelectedKey = useMemo(
    () => (selectedKey !== undefined ? selectedKey : internalSelectedKey),
    [selectedKey, internalSelectedKey]
  );

  // 展开/收起节点
  const handleToggle = useCallback((key: string) => {
    setExpandedKeys((prev) => {
      const isExpanded = prev.includes(key);
      if (isExpanded) {
        return prev.filter((k) => k !== key);
      } else {
        return [...prev, key];
      }
    });
  }, []);

  // 选择节点
  const handleSelect = useCallback(
    (key: string, node: TreeNode) => {
      const newSelectedKey = currentSelectedKey === key ? null : key;

      if (selectedKey === undefined) {
        setInternalSelectedKey(newSelectedKey);
      }

      onSelect?.(newSelectedKey, newSelectedKey ? node : null);
    },
    [onSelect, selectedKey, currentSelectedKey]
  );

  // 递归渲染树节点
  const renderTreeNodes = useMemo(() => {
    return treeData.map((node) => (
      <TreeNodeComponent
        key={node.key}
        node={node}
        level={0}
        expandedKeys={expandedKeys}
        selectedKey={currentSelectedKey}
        onToggle={handleToggle}
        onSelect={handleSelect}
      />
    ));
  }, [treeData, expandedKeys, currentSelectedKey, handleToggle, handleSelect]);

  return <div className="custom-tree">{renderTreeNodes}</div>;
};

export default CustomTree;
