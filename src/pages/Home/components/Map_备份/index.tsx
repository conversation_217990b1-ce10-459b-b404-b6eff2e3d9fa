import React, { memo, useEffect, useMemo, useRef, useState } from "react";
import { Label, Map, Polygon } from "react-bmapgl";
import mapJson from "@/assets/mapStyle.json";
import { useEventListener } from "ahooks";
import { convertGeoJSONToPolygonData, type PolygonData } from "@/utils";
import { useRequest } from "ahooks";
import useStore from "@/store";
import {
  CONTAINER_HEIGHT,
  CONTAINER_SCALE_FACTOR,
  CONTAINER_WIDTH,
} from "@/utils/config";
import { Spin } from "antd";

function BaiduMap() {
  const mapRef = useRef<BMapGL.Map | undefined>(undefined);

  const [loading, setLoading] = useState(true);

  const selectCode = useStore((state) => state.selectCode);

  const enableDragging = useStore((state) => state.enableDragging);

  // 底图瓦片加载完成
  useEventListener(
    "tilesloaded",
    () => {
      setLoading(false);
      useStore.setState((draft) => {
        draft.loading = false;
      });
    },
    { target: mapRef as any }
  );

  const { data: regionData } = useRequest(
    async () => {
      try {
        const [resFull, resData] = await Promise.all([
          fetch(`/screen/areaData/${selectCode}_full.json`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }),
          fetch(`/screen/mapData/${selectCode}.json`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }),
        ]);

        const [areaData, mapData] = await Promise.all([
          resFull.json(),
          resData.json(),
        ]);

        return { areaData, mapData };
      } catch (error) {
        console.error("请求失败:", error);
        throw error;
      }
    },
    {
      refreshDeps: [selectCode],
    }
  );

  // 转换 GeoJSON 数据为 Polygon 组件需要的格式 拆分为多个区域渲染
  const polygonRegionData = useMemo(() => {
    return convertGeoJSONToPolygonData(regionData?.areaData);
  }, [regionData?.areaData]);

  // 省市Polygon渲染数据 渲染整体的那个 用于边框颜色加重和聚焦autoViewport
  const polygonMapData = useMemo(() => {
    return convertGeoJSONToPolygonData(regionData?.mapData, true);
  }, [regionData?.mapData]);

  const opacityList = useMemo(() => {
    // 翻转数组
    return [0.1, 0.2, 0.3, 0.4, 0.5].reverse();
  }, []);

  // 获取当前城市
  useEffect(() => {
    if (!mapRef.current || !polygonMapData || polygonMapData.length === 0) {
      return;
    }

    // 是不是全国
    const isChina = selectCode === 100000;

    // 收集所有多边形的点坐标，用于设置视野
    const pointArray: BMapGL.Point[] = [];

    polygonMapData.forEach((polygonData: PolygonData) => {
      // 将每个多边形的路径点添加到数组中
      pointArray.push(...polygonData.path);
    });

    const marginTopValue = isChina
      ? -80 / CONTAINER_SCALE_FACTOR
      : 120 / CONTAINER_SCALE_FACTOR;

    if (pointArray.length > 0) {
      // 调整视野以包含所有点
      mapRef.current.setViewport(pointArray, {
        enableAnimation: undefined,
        delay: 0,
        zoomFactor: isChina ? -0.3 : -0.1,
        margins: [marginTopValue, 0, 0, 0],
      });
    }
  }, [polygonMapData, selectCode]);

  const nameList = useMemo(() => {
    const value: Page.NameItemType[] =
      regionData?.areaData?.features?.map?.((item: any) => ({
        name: item.properties?.name,
        centroid: item.properties?.centroid,
        adcode: item.properties?.adcode,
      })) ?? [];
    return value?.filter((val) => val.name && val.centroid);
  }, [regionData?.areaData]);

  return (
    <React.Fragment>
      <Spin spinning={loading} className="centered" />
      <div
        className="absolute inset-x-0 inset-y-0 z-0"
        style={{
          zoom: CONTAINER_SCALE_FACTOR,
          opacity: loading ? 0 : 1,
        }}
      >
        <Map
          ref={(value) => {
            mapRef.current = value?.map;
            useStore.setState((draft) => {
              draft.mapRefState = value?.map;
            });
            const map=mapRef.current!;
            if(map){
              const start = new BMapGL.Point(117.44,39.01);
              const end = new BMapGL.Point(117.12,39.21);
              const zoom = map.getViewport([start, end]).zoom;
              const center = map.getViewport([start, end]).center;
              // map.setCenter(center);
              // map.setZoom(zoom);
              const busline = new BMapGL.BusLineSearch(map, {
                renderOptions: { map: map,selectFirstResult:false,highlightMode:2},
                onGetBusListComplete: function (result) {
                  if (result) {
                    const fstLine = result.getBusListItem(0); //获取第一个公交列表显示到map上
                    busline.getBusLine(fstLine);
                  }
                },
              });
              // busline.getBusList("天津地铁1号线");
            }
          }}
          style={{
            height: CONTAINER_HEIGHT,
            width: CONTAINER_WIDTH,
          }}
          mapStyleV2={{
            styleJson: mapJson,
          }}
          center={new BMapGL.Point(116.405285, 39.904989)}
          // center={} // 北京市中心
          zoom={5}
          heading={0}
          tilt={0}
          // 允许拖拽
          enableDragging={enableDragging}
          enableScrollWheelZoom
        />

        {!loading &&
          polygonMapData.map((polygonData: PolygonData) => (
            <Polygon
              key={polygonData.id}
              path={polygonData.path}
              map={mapRef.current!}
              strokeColor="#FFFFFF"
              strokeOpacity={1}
              strokeWeight={1.2}
              fillColor="#222f40"
              fillOpacity={1}
            />
          ))}

        {!loading &&
          polygonRegionData.map((polygonData: PolygonData, polygonIndex) => (
            <Polygon
              key={polygonData.id}
              path={polygonData.path}
              map={mapRef.current!}
              strokeColor="#FFFFFF"
              strokeWeight={1}
              strokeOpacity={0.4}
              fillColor="#5FEAFF"
              fillOpacity={opacityList[polygonIndex % opacityList.length]}
              // onClick={() => {
              //   console.log("点击了", polygonData.name);
              // }}
            />
          ))}

        {!loading &&
          nameList.map((val) => (
            <Label
              key={val.adcode}
              // position={new BMapGL.Point(116.4, 39.91)}
              position={new BMapGL.Point(val.centroid[0], val.centroid[1])}
              text={val.name}
              map={mapRef.current!}
              offset={new BMapGL.Size(-10, 0)}
              style={{
                fontSize: "11px",
                color: "rgba(255,255,255,0.75)",
                border: "none",
                background: "none",
                fontWeight: "normal",
              }}
            />
          ))}
      </div>
    </React.Fragment>
  );
}

export default memo(BaiduMap);
