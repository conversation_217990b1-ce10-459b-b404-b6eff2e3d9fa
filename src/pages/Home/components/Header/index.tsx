import logo from "@/assets/logo.png";
import { memo, useState, useEffect } from "react";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { useInterval } from "ahooks";

dayjs.locale("zh-cn");

function Header() {
  const [now, setNow] = useState(dayjs());
  const [delay, setDelay] = useState<number | undefined>(undefined);

  // 计算到下一分钟整点的延迟时间
  useEffect(() => {
    const currentTime = dayjs();
    const nextMinute = currentTime.add(1, "minute").startOf("minute");
    const delayToNextMinute = nextMinute.diff(currentTime);

    // 先延迟到下一分钟整点，然后开始每分钟更新
    setTimeout(() => {
      setNow(dayjs());
      setDelay(60000); // 设置为每分钟更新
    }, delayToNextMinute);
  }, []);

  useInterval(
    () => {
      setNow(dayjs());
    },
    delay,
    {
      immediate: true,
    }
  );

  const weekText = now.format("dddd"); // 星期一
  const dateText = now.format("YYYY-MM-DD"); // 2025-06-21
  const timeText = now.format("HH:mm"); // 12:00

  return (
    <header className="h-[180px] absolute px-[100px] w-full flex items-end justify-between z-20">
      <div className="flex items-center space-x-[38px]">
        <img src={logo} className="h-[88px] object-fill" alt="logo" />
        <span className="text-[80px]">中国联通移动网连接智能编排系统</span>
      </div>
      <div className="flex items-center space-x-[30px]">
        <div className="text-right">
          <div className="text-[32px] leading-[48px]">{weekText}</div>
          <div className="text-[28px] leading-[42px]">{dateText}</div>
        </div>
        <div className="w-[1px] bg-[rgba(255,255,255,0.52)] h-[80px]"></div>
        <div className="text-[82px]">{timeText}</div>
      </div>
    </header>
  );
}
export default memo(Header);
