import React, { memo, useMemo, useRef, useState } from "react";
import { Map } from "react-bmapgl";
import mapJson from "@/assets/mapStyle.json";
import { useEventListener } from "ahooks";
import { convertGeoJSONToPolygonData, type PolygonData } from "@/utils";
import { useRequest } from "ahooks";
import useStore from "@/store";
import {
  CONTAINER_HEIGHT,
  CONTAINER_SCALE_FACTOR,
  CONTAINER_WIDTH,
} from "@/utils/config";
import { Spin } from "antd";
import useSubway from "@/hooks/useSubway";

function BaiduMap() {
  const mapRef = useRef<BMapGL.Map | undefined>(undefined);

  const [loading, setLoading] = useState(true);

  const selectCode = useStore((state) => state.selectCode);

  const enableDragging = useStore((state) => state.enableDragging);

  // const [overlay, setOverlay] = useState<BMapGL.Polygon[]>([]);

  const { searchBusLine } = useSubway();

  // 底图瓦片加载完成
  useEventListener(
    "tilesloaded",
    () => {
      setLoading(false);
      useStore.setState((draft) => {
        draft.loading = false;
      });
    },
    { target: mapRef as any }
  );

  const onInitLabels = (areaData: any) => {
    const value: Page.NameItemType[] =
      areaData?.features?.map?.((item: any) => ({
        name: item.properties?.name,
        centroid: item.properties?.centroid ?? item.properties?.center,
        adcode: item.properties?.adcode,
      })) ?? [];

    const nameList = value?.filter((val) => val.name && val.centroid);

    const labels = nameList.reduce<BMapGL.Label[]>((acc, val) => {
      const label = new BMapGL.Label(val.name, {
        position: new BMapGL.Point(val.centroid[0], val.centroid[1]),
        offset: new BMapGL.Size(-10, 0),
      });

      label.setStyle({
        fontSize: "11px",
        color: "rgba(255,255,255,0.75)",
        background: "none",
        fontWeight: "normal",
        border: "none",
      });
      acc.push(label);
      return acc;
    }, []);

    // 批量添加到地图
    labels.forEach((label) => mapRef.current?.addOverlay(label));
  };

  const legendList = useMemo(() => {
    return [
      {
        min: 0,
        max: 6,
        unit: "万个",
        color: "#98d2f6",
      },
      {
        min: 6,
        max: 12,
        unit: "万个",
        color: "#91D5FF",
      },
      {
        min: 12,
        max: 18,
        unit: "万个",
        color: "#69C0FF",
      },
      {
        min: 18,
        max: 24,
        unit: "万个",
        color: "#1990FF",
      },
      {
        min: 24,
        max: 30,
        unit: "万个",
        color: "#0A6DD9",
      },
    ];
  }, []);

  // 地图聚焦视野
  const onViewport = (mapData: any) => {
    // 是不是全国
    const isChina = selectCode === 100000;

    const polygonMapData = convertGeoJSONToPolygonData(mapData, true);

    // 收集所有多边形的点坐标，用于设置视野
    const pointArray: BMapGL.Point[] = [];

    polygonMapData.forEach((polygonData: PolygonData) => {
      // 将每个多边形的路径点添加到数组中
      pointArray.push(...polygonData.path);
    });

    const marginTopValue = isChina ? 0 : 120 / CONTAINER_SCALE_FACTOR;

    if (pointArray.length > 0) {
      // 调整视野以包含所有点
      const viewport = mapRef.current?.getViewport(pointArray, {
        enableAnimation: false,
        delay: 0,
        zoomFactor: isChina ? -0.26 : -0.16,
        margins: [marginTopValue, 100, 0, 0],
      });
      // @ts-expect-error
      mapRef.current?.setCenter(viewport?.center, { noAnimation: true });
      // @ts-expect-error
      mapRef.current?.setZoom(viewport.zoom, { noAnimation: true });
    }
  };

  const onInitPolygonMap = (mapData: any) => {
    // 省市Polygon渲染数据 渲染整体的那个 用于边框颜色加重
    const polygonMapData = convertGeoJSONToPolygonData(mapData, true);

    const polygons = polygonMapData.reduce<BMapGL.Polygon[]>((acc, item) => {
      const polygon = new BMapGL.Polygon(item.path, {
        strokeColor: "#FFFFFF",
        strokeWeight: 1.2,
        strokeOpacity: 1,
        fillColor: "#222f40",
        fillOpacity: 1,
      });
      acc.push(polygon);
      return acc;
    }, []);
    polygons.forEach((polygon) => {
      mapRef.current?.addOverlay(polygon);
    });
  };

  const onInitPolygonRegion = (areaData: any) => {
    // 转换 GeoJSON 数据为 Polygon 组件需要的格式 拆分为多个区域渲染
    const list = convertGeoJSONToPolygonData(areaData);
    const polygons = list.reduce<BMapGL.Polygon[]>((acc, item, index) => {
      const polygon = new BMapGL.Polygon(item.path, {
        strokeColor: "#FFFFFF",
        strokeWeight: 1,
        strokeOpacity: 0.4,
        fillColor: legendList[index % legendList.length].color,
        fillOpacity: 1,
      });
      acc.push(polygon);
      return acc;
    }, []);
    polygons.forEach((polygon) => {
      mapRef.current?.addOverlay(polygon);
    });
  };

  useRequest(
    async () => {
      try {
        const [resFull, resData] = await Promise.all([
          fetch(`/screen/areaData/${selectCode}_full.json`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }),
          fetch(`/screen/mapData/${selectCode}.json`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }),
        ]);

        const [areaData, mapData] = await Promise.all([
          resFull.json(),
          resData.json(),
        ]);

        return { areaData, mapData };
      } catch (error) {
        console.error("请求失败:", error);
        throw error;
      }
    },
    {
      refreshDeps: [selectCode],
      onSuccess: async (res) => {
        // 清除之前的覆盖物
        mapRef.current?.clearOverlays();
        // clearOverlays();
        onInitLabels(res.areaData);
        onInitPolygonMap(res.mapData);
        onInitPolygonRegion(res.areaData);
        onViewport(res.mapData);
        const map = mapRef.current!;
        if (map) {
          const busLineSearch = searchBusLine("北京市", "2号线", {
            onGetBusLineComplete: (result) => {
              if (result) {
                // 获取地铁线路的路径点
                const polyline = result.getPolyline();
                // 定义地铁线路颜色
                const lineColor = "#FF0026"; // 可以根据不同线路设置不同颜色

                // 绘制地铁线路
                const subwayLine = new BMapGL.Polyline(polyline.getPath(), {
                  strokeColor: lineColor,
                  strokeWeight: 6,
                  strokeOpacity: 1,
                });
                subwayLine.disableMassClear();
                map.addOverlay(subwayLine);

                // 获取并绘制地铁站点
                const stationCount = result.getNumBusStations();
                for (let i = 0; i < stationCount; i++) {
                  const station = result.getBusStation(i);
                  const stationPoint = station.position;
                  const stationName = station.name;

                  // 绘制站点标记
                  const marker = new BMapGL.Marker(stationPoint);
                  marker.disableMassClear();
                  map.addOverlay(marker);

                  // 添加站点名称标签
                  const label = new BMapGL.Label(stationName, {
                    position: stationPoint,
                    offset: new BMapGL.Size(-20, 25),
                  });
                  label.setStyle({
                    color: "#fff",
                    fontSize: "12px",
                    backgroundColor: "rgba(0,0,0,0.5)",
                    border: "none",
                    padding: "2px 6px",
                    borderRadius: "3px",
                  });
                  label.disableMassClear();
                  map.addOverlay(label);
                }
              }
            },
          });
        }
      },
    }
  );

  return (
    <React.Fragment>
      <Spin spinning={loading} className="centered" />
      <div
        className="absolute inset-x-0 inset-y-0 z-0"
        style={{
          zoom: CONTAINER_SCALE_FACTOR,
          opacity: loading ? 0 : 1,
        }}
      >
        <Map
          ref={(value) => {
            mapRef.current = value?.map;
            useStore.setState((draft) => {
              draft.mapRefState = value?.map;
            });
          }}
          style={{
            height: CONTAINER_HEIGHT,
            width: CONTAINER_WIDTH,
          }}
          mapStyleV2={{
            styleJson: mapJson,
          }}
          center={new BMapGL.Point(116.405285, 39.904989)}
          zoom={5}
          heading={0}
          tilt={0}
          // 允许拖拽
          enableDragging={enableDragging}
          enableScrollWheelZoom
        />
      </div>
      {/* <div className="absolute shadow-[0px_0px_10px_1px_rgba(0,0,0,0.04)] bottom-[100px] left-[2084px] w-[330px] px-[54px] py-[36px] bg-[#2A4160] space-y-[24px]">
        {legendList.map((val, index) => (
          <div
            className="h-[54px] flex items-center"
            key={`legend${index + 1}`}
          >
            <div
              className="w-[56px] h-[30px]"
              style={{
                background: val.color,
              }}
            ></div>
            <div className="ml-5 text-[26px]">
              {val.min}-{val.max}&nbsp;
              {val.unit}
            </div>
          </div>
        ))}
      </div>
      <div className="absolute right-[2190px] space-y-[40px] top-[694px]">
        {statisticsList.map((val, index) => (
          <div
            className="h-[160px] flex items-center"
            key={`statistics-${index}`}
          >
            <div className="w-[165px] h-full rounded-[24px_0_0_24px] bg-[#2A4160] flex items-center justify-center">
              <FundFilled className="text-[70px] text-[#5FEAFF]!" />
            </div>
            <div className="w-[344px] h-full ml-3 rounded-[0_24px_24px_0] bg-[#2A4160] flex justify-center flex-col px-[26px]">
              <span className="text-[42px] text-white mt-1">{val.name}</span>
              <div className="flex items-baseline mt-1">
                <span className="text-[#5FEAFF] text-[52px] value-font">
                  {val.value}
                </span>
                <span className="text-[26px] ml-4">万个</span>
              </div>
            </div>
          </div>
        ))}
      </div> */}
    </React.Fragment>
  );
}

export default memo(BaiduMap);
