import Card from "@/components/Card";
import { SceneTypeEnum } from "@/enum";
import useStore from "@/store";
import { useMemo } from "react";
import clsx from "clsx";

function SceneList() {
  const list = useMemo(() => {
    return [
      {
        name: "住宅小区",
        value: 200,
        key: SceneTypeEnum.ResidentialCommunity,
      },
      {
        name: "重点交通干线",
        value: 99,
        key: SceneTypeEnum.KeyTransportation,
      },
      {
        name: "交通枢纽",
        value: 9,
        key: SceneTypeEnum.TransportationHubs,
      },
      {
        name: "地铁",
        value: 23,
        key: SceneTypeEnum.Subway,
      },
      {
        name: "重点商超",
        value: 54,
        key: SceneTypeEnum.KeyShopping,
      },
      {
        name: "商务楼宇",
        value: 23,
        key: SceneTypeEnum.BusinessBuilding,
      },
      {
        name: "政务中心",
        value: 23,
        key: SceneTypeEnum.GovernmentCenter,
      },
      {
        name: "高等学校",
        value: 3,
        key: SceneTypeEnum.HighSchool,
      },
      {
        name: "文旅景区",
        value: 22,
        key: SceneTypeEnum.CultureAndTourism,
      },
      {
        name: "医疗机构",
        value: 53,
        key: SceneTypeEnum.MedicalInstitution,
      },
    ];
  }, []);

  const sceneType = useStore((state) => state.sceneType);

  return (
    <Card
      title="十大场景"
      className="absolute w-[554px] right-[1536px] top-[280px]"
    >
      <div className="h-[1674px] py-[40px] px-[24px] space-y-[32px]">
        {list.map((val) => (
          <div
            className={clsx(
              "px-[8px] h-[130px] cursor-pointer rounded-[12px] text-[rgba(255,255,255,0.85)] text-[36px] flex items-center justify-between",
              {
                "hover:bg-[rgba(255,255,255,0.05)]": sceneType !== val.key,
                "bg-[rgba(95,234,255,0.15)]": sceneType === val.key,
              }
            )}
            key={val.key}
            onClick={() => {
              useStore.setState((draft) => {
                draft.sceneType =
                  val.key === draft.sceneType ? undefined : val.key;
              });
            }}
          >
            <span className="pl-[32px]">{val.name}</span>
            <div className="flex items-center">
              <span className="text-[42px] text-white">{val.value}</span>
              <span className="pr-[24px]">万个</span>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}
export default SceneList;
