import { memo, useEffect, useRef, useState } from "react";
import { useExternal, useMount } from "ahooks";
import useStore from "@/store";
import type { Translation } from "react-map-interaction";
import { MapInteractionCSS } from "react-map-interaction";

function SubwayMap() {
  const subwayRef = useRef<SubwayInstance>();
  const domRef = useRef<HTMLDivElement>(null);

  const subwayScaleValue = useStore((state) => state.subwayScaleValue);

  const [translationValue, setTranslationValue] = useState<Translation>({
    x: 0,
    y: 50,
  });

  const enableDragging = useStore((state) => state.enableDragging);

  const status = useExternal(
    "https://api.map.baidu.com/api?type=subway&v=1.0&ak=1XjLLEhZhQNUzd93EjU5nOGQ",
    {
      js: {
        async: true,
      },
      type: "js",
    }
  );

  useEffect(() => {
    if (status === "ready") {
      // 尝试传入配置选项
      setTimeout(() => {
        const subway = new BMapSub.Subway("subway-container", "131");
        console.warn(subway, "subway", BMapSub.Subway);
        console.warn(
          BMapSub.SubwayCitiesList,
          "BMapSub.SubwayCitiesList;BMapSub.SubwayCitiesList;"
        );
        subway.addEventListener("subwayloaded", function () {
          const drct = new BMapSub.Direction(subway);
          drct.search("西直门", "西红门");
          // subway.setZoom(1.5);
        });
        // console.warn(subway);
      }, 100);
    }
  }, [status]);

  // useMount(() => {
  //   // subwayRef.current = subway("subway-container", {
  //   //   adcode: "1200",
  //   //   easy: true,
  //   //   height: 2180,
  //   //   width: 4000,
  //   // });

  //   // subwayRef.current.event.on("subway.complete", function () {
  //   //   console.log("地铁图加载完成，已自动适应视图大小并居中");
  //   //   // 如果需要显示特定线路，可以取消注释下面的代码
  //   //   const svgDom = domRef.current?.querySelector("#subway-svg");
  //   //   if (svgDom) {
  //   //     subwayRef.current?.setFitView(svgDom);
  //   //   }
  //   //   subwayRef.current?.showLine("10号线");
  //   //   //   setTimeout(() => {
  //   //   //     subwayRef.current?.setFitView(domRef.current?.querySelector("#g-line-name"))
  //   //   //   }, 1000);
  //   // });
  //   const list = BMapSub.SubwayCitiesList;

  //   console.warn(list,"王明远");

  //   const subway = new BMapSub.Subway("subway-container", '332');

  //   // 添加手动居中功能（用于测试）
  //   // window.centerSubway = function () {
  //   //   var svgElement = document.getElementById("subway-svg");
  //   //   if (svgElement) {
  //   //     mysubway.setFitView(svgElement);
  //   //   }
  //   // };
  // });

  return (
    <div className="centered w-[7680px] h-[2160px]" id="subway-box">
      <div
        className="w-[7680px] h-[2160px] cursor-pointer! bg-transparent!"
        id="subway-container"
        ref={domRef}
      ></div>
      {/* <MapInteractionCSS
        disablePan={!enableDragging}
        value={{
          scale: subwayScaleValue,
          translation: translationValue,
        }}
        showControls
        onChange={(e) => {
          setTranslationValue(e.translation);
          useStore.setState((draft) => {
            draft.subwayScaleValue = e.scale;
          });
        }}
      >
        <div
          className="w-[4000px] h-[2160px] cursor-pointer!"
          id="subway-container"
          ref={domRef}
        ></div>
      </MapInteractionCSS> */}
    </div>
  );
}
export default memo(SubwayMap);
