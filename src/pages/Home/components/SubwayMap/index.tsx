import { memo, useEffect, useRef, useState } from "react";
import { useMount } from "ahooks";
import useStore from "@/store";
import type { Translation } from "react-map-interaction";
import { MapInteractionCSS } from "react-map-interaction";

function SubwayMap() {
  const subwayRef = useRef<SubwayInstance>();
  const domRef = useRef<HTMLDivElement>(null);

  const subwayScaleValue = useStore((state) => state.subwayScaleValue);

  const selectLineId = useStore((state) => state.selectLineId);

  const [ready, setReady] = useState(false);

  const [translationValue, setTranslationValue] = useState<Translation>({
    x: 0,
    y: 50,
  });

  const enableDragging = useStore((state) => state.enableDragging);

  useMount(() => {
    subwayRef.current = subway("subway-container", {
      adcode: "1200",
      easy: true,
      height: 2160,
      width: 4000,
    });

    subwayRef.current.event.on("subway.complete", function () {
      console.log("地铁图加载完成，已自动适应视图大小并居中");
      const svgDom = domRef.current?.querySelector("#subway-svg");
      if (svgDom) {
        subwayRef.current?.setFitView(svgDom);
      }
      subwayRef.current?.getLineList((val) => {
        useStore.setState((draft) => {
          draft.subwayLineList = val;
        });
      });
      setReady(true);
    });
  });

  useEffect(() => {
    if (ready && selectLineId) {
      subwayRef.current?.showLine(selectLineId);
    } else if (ready && selectLineId === undefined) {
      subwayRef.current?.clearLine();
    }
    return () => {};
  }, [ready, selectLineId]);

  return (
    <div className="centered w-[4000px] h-[2160px]" id="subway-box">
      <MapInteractionCSS
        disablePan={!enableDragging}
        value={{
          scale: subwayScaleValue,
          translation: translationValue,
        }}
        showControls
        onChange={(e) => {
          setTranslationValue(e.translation);
          useStore.setState((draft) => {
            draft.subwayScaleValue = e.scale;
          });
        }}
      >
        <div
          className="w-[4000px] h-[2160px] cursor-pointer!"
          id="subway-container"
          ref={domRef}
        ></div>
      </MapInteractionCSS>
    </div>
  );
}
export default memo(SubwayMap);
