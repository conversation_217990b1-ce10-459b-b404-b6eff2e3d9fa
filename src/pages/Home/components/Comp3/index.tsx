import {
  CONTAINER_HEIGHT,
  CONTAINER_SCALE_FACTOR,
  CONTAINER_WIDTH,
} from "@/utils/config";
import { Map } from "react-bmapgl";
import mapJson from "@/assets/mapStyle.json";
import { useRef, useState, useEffect, useCallback } from "react";

function Comp3() {
  const mapRef = useRef<BMapGL.Map | undefined>(undefined);
  const [selectedLine, setSelectedLine] = useState("天津地铁1号线");
  const [busLineSearch, setBusLineSearch] =
    useState<BMapGL.BusLineSearch | null>(null);

  // 地铁线路配置
  const subwayLines = [
    {
      name: "天津地铁1号线",
      searchKey: "天津地铁1号线",
      startStation: "刘园",
      endStation: "双林",
      city: "天津市",
    },
    {
      name: "北京地铁6号线",
      searchKey: "北京地铁6号线",
      startStation: "金安桥",
      endStation: "潞城",
      city: "北京市",
    },
    {
      name: "天津地铁3号线",
      searchKey: "天津地铁3号线",
      startStation: "小淀",
      endStation: "南站",
      city: "天津市",
    },
    {
      name: "天津地铁5号线",
      searchKey: "天津地铁5号线",
      startStation: "北辰科技园北",
      endStation: "梨园头",
      city: "天津市",
    },
    {
      name: "天津地铁6号线",
      searchKey: "天津地铁6号线",
      startStation: "南孙庄",
      endStation: "梅林路",
      city: "天津市",
    },
  ];

  // 初始化地图和公交线路搜索
  useEffect(() => {
    if (mapRef.current) {
      const map = mapRef.current;
      const start = "安桥河北";
      const end = "天宫院";

      // 获取start和end的坐标
      const geocoder = new BMapGL.Geocoder();

      // 获取起点坐标
      geocoder.getPoint(
        start,
        (startPoint: BMapGL.Point) => {
          if (startPoint) {
            console.log(`${start}坐标:`, startPoint);

            // 获取终点坐标
            geocoder.getPoint(
              end,
              (endPoint: BMapGL.Point) => {
                if (endPoint) {
                  console.log(`${end}坐标:`, endPoint);

                  // 在地图上添加起点和终点标记
                  const startMarker = new BMapGL.Marker(startPoint);
                  const endMarker = new BMapGL.Marker(endPoint);

                  map.addOverlay(startMarker);
                  map.addOverlay(endMarker);

                  // 添加标签
                  const startLabel = new BMapGL.Label(start, {
                    position: startPoint,
                    offset: new BMapGL.Size(0, -30),
                  });
                  const endLabel = new BMapGL.Label(end, {
                    position: endPoint,
                    offset: new BMapGL.Size(0, -30),
                  });

                  startLabel.setStyle({
                    color: "#333",
                    fontSize: "12px",
                    backgroundColor: "rgba(255,255,255,0.8)",
                    border: "1px solid #ccc",
                    borderRadius: "3px",
                    padding: "2px 5px",
                  });

                  endLabel.setStyle({
                    color: "#333",
                    fontSize: "12px",
                    backgroundColor: "rgba(255,255,255,0.8)",
                    border: "1px solid #ccc",
                    borderRadius: "3px",
                    padding: "2px 5px",
                  });

                  // map.addOverlay(startLabel);
                  // map.addOverlay(endLabel);

                  // 调整地图视野以包含两个点
                  // map.setViewport([startPoint, endPoint]);
                  const start = new BMapGL.Point(117.44,39.01);
                  const end = new BMapGL.Point(117.12,39.21);
                  const zoom = map.getViewport([start, end]).zoom;
                  const center = map.getViewport([start, end]).center;
                  map.setCenter(center);
                  map.setZoom(zoom);
                  const busline = new BMapGL.BusLineSearch(map, {
                    renderOptions: { map: map},
                    onGetBusListComplete: function (result) {
                      if (result) {
                        const fstLine = result.getBusListItem(0); //获取第一个公交列表显示到map上
                        busline.getBusLine(fstLine);
                      }
                    },
                  });
                  busline.getBusList("天津地铁1号线");

                  // const busSearch = new BMapGL.TransitRoute(map, {
                  //   renderOptions: { map: map },
                  //   // policy:3,
                  //   onSearchComplete: (res) => {
                  //     console.warn(res, "sssss");
                  //     // const TransitRoutePlan=res.getPlan(0);
                  //     // const line=TransitRoutePlan.getLine(0);
                  //     // console.warn(line,'lineline')
                  //   },
                  // });
                  // // alert(BMAP_TRANSIT_POLICY_FIRST_SUBWAYS)
                  // console.warn(startPoint, endPoint, "啊啊啊啊");
                  // busSearch.setPolicy(5);
                  // busSearch.disableAutoViewport();
                  // busSearch.search(start, end);
                } else {
                  console.error(`无法获取${end}的坐标`);
                }
              },
              "北京市"
            );
          } else {
            console.error(`无法获取${start}的坐标`);
          }
        },
        "北京市"
      );

      // 创建公交线路搜索实例
      const busSearch = new BMapGL.TransitRoute(map, {
        renderOptions: { map: map, panel: "#result" },
        onSearchComplete: (res) => {
          console.warn(res, "sssss");
        },
        // onGetBusListComplete: (result: any) => {
        //   console.log("公交线路列表:", result);
        //   if (result && result.getNumBusLines() > 0) {
        //     // 获取第一条线路的详细信息
        //     const busLine = result.getBusLine(0);
        //     busSearch.getBusLine(busLine);
        //   }
        // },
        // onGetBusLineComplete: (busLine: any) => {
        //   console.log("公交线路详情:", busLine);
        //   if (busLine) {
        //     // 清除之前的覆盖物
        //     map.clearOverlays();

        //     // 创建折线显示线路
        //     const points = [];
        //     const stations = busLine.getStations();

        //     for (let i = 0; i < stations.length; i++) {
        //       const station = stations[i];
        //       points.push(station.position);

        //       // 添加站点标记
        //       const marker = new BMapGL.Marker(station.position);
        //       map.addOverlay(marker);

        //       // 添加站点标签
        //       const label = new BMapGL.Label(station.name, {
        //         position: station.position,
        //         offset: new BMapGL.Size(0, -30),
        //       });
        //       label.setStyle({
        //         color: "#333",
        //         fontSize: "12px",
        //         backgroundColor: "rgba(255,255,255,0.8)",
        //         border: "1px solid #ccc",
        //         borderRadius: "3px",
        //         padding: "2px 5px",
        //       });
        //       map.addOverlay(label);
        //     }

        //     // 绘制线路
        //     if (points.length > 1) {
        //       const polyline = new BMapGL.Polyline(points, {
        //         strokeColor: "#D63441",
        //         strokeWeight: 6,
        //         strokeOpacity: 0.8,
        //       });
        //       map.addOverlay(polyline);
        //     }

        //     // 调整地图视野
        //     if (points.length > 0) {
        //       map.setViewport(points);
        //     }
        //   }
        // },
      });

      // setBusLineSearch(busSearch);

      // // 默认搜索天津地铁1号线
      // setTimeout(() => {
      //   searchSubwayLine("天津地铁1号线");
      // }, 100);
    }
  }, []);

  // 处理线路切换
  const handleLineChange = (lineName: string) => {
    // setSelectedLine(lineName);
    // searchSubwayLine(lineName);
  };

  return (
    <div
      className="absolute inset-x-0 inset-y-0 z-0"
      id="tianjinSubwayMap"
      style={{
        zoom: CONTAINER_SCALE_FACTOR,
      }}
    >
      {/* 线路选择控件 */}
      {/* <div className="absolute top-4 left-4 z-10 bg-white p-4 rounded-lg shadow-lg">
        <div className="mb-3">
          <label className="block text-sm font-medium mb-2">
            选择地铁线路:
          </label>
          <select
            value={selectedLine}
            onChange={(e) => handleLineChange(e.target.value)}
            className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {subwayLines.map((line) => (
              <option key={line.name} value={line.searchKey}>
                {line.name}
              </option>
            ))}
          </select>
        </div>

        <div className="text-sm text-gray-600">当前显示: {selectedLine}</div>
      </div> */}

      <Map
        style={{
          width: CONTAINER_WIDTH,
          height: CONTAINER_HEIGHT,
        }}
        ref={(value) => {
          mapRef.current = value?.map;
        }}
        zoom={12}
        mapStyleV2={{
          styleJson: mapJson,
        }}
        // center={new BMapGL.Point(117.2, 39.1)} // 天津市中心
        // center={}
        center={new BMapGL.Point(116.405285, 39.904989)}
        enableScrollWheelZoom={true}
        enableDragging={true}
        enableDoubleClickZoom={true}
      />
      <div
        id="result"
        className=" absolute h-[400px] w-[1000px] bg-[red] z-[1110]"
      ></div>
    </div>
  );
}
export default Comp3;
