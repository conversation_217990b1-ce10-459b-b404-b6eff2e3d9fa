import Card from "@/components/Card";
import useStore from "@/store";
import React, { memo } from "react";
import gripperIcon from "@/assets/gripper.svg";

/**
 * 大左和大右公共的
 */
function Common() {
  const mapRefState = useStore((state) => state.mapRefState);

  // 地图缩小方法
  const onZoomOut = () => {
    mapRefState?.zoomOut();
    useStore.setState((draft) => {
      if (draft.subwayScaleValue >= 0.4) {
        draft.subwayScaleValue = draft.subwayScaleValue - 0.2;
      }
    });
  };

  // 地图放大方法
  const onZoomIn = () => {
    mapRefState?.zoomIn();
    useStore.setState((draft) => {
      if (draft.subwayScaleValue <= 2.4) {
        draft.subwayScaleValue = draft.subwayScaleValue + 0.2;
      }
    });
  };

  // 切换是否开启拖拽
  const onToggleDragging = () => {
    useStore.setState((draft) => {
      draft.enableDragging = !draft.enableDragging;
    });
  };

  const checked = useStore((state) => state.enableDragging);

  return (
    <React.Fragment>
      <Card
        title="网络情况"
        className="absolute w-[1336px] left-[100px] top-[280px]"
      >
        <div className="h-[490px]"></div>
      </Card>

      <Card
        title="连接质量"
        className="absolute w-[1336px] left-[100px] top-[888px]"
      >
        <div className="h-[490px]"></div>
      </Card>
      <Card
        title="昨日分省连接Top10"
        className="absolute w-[1336px] left-[100px] bottom-[86px]"
      >
        <div className="h-[460px]"></div>
      </Card>

      <Card
        title="用户情况"
        className="absolute w-[1336px] right-[100px] top-[280px]"
      >
        <div className="h-[490px]"></div>
      </Card>

      <Card
        title="使用情况"
        className="absolute w-[1336px] right-[100px] top-[888px]"
      >
        <div className="h-[490px]"></div>
      </Card>

      <Card
        title="当前告警"
        className="absolute w-[1336px] right-[100px] bottom-[86px]"
      >
        <div className="h-[460px]"></div>
      </Card>

      {/* <div className="w-[470px] h-[140px] bg-[rgba(94,102,111,0.46)] bottom-[100px] left-[2104px] flex justify-center space-x-[48px] items-center absolute">
        <span className="w-[10px] bg-[#5FEAFF] absolute left-0 h-full"></span>
        <div
          className="size-[88px] relative cursor-pointer"
          onClick={onZoomOut}
        >
          <span className="w-[66px] h-[10px] bg-[#5FEAFF] centered"></span>
        </div>
        <div
          className="size-[88px] relative cursor-pointer transition-all"
          style={{
            background: checked ? "rgba(95,234,255,.2)" : "none",
          }}
          onClick={onToggleDragging}
        >
          <img className="centered w-[70px]" src={gripperIcon} alt="" />
        </div>
        <div className="size-[88px] relative cursor-pointer" onClick={onZoomIn}>
          <span className="w-[66px] h-[10px] bg-[#5FEAFF] centered"></span>
          <span className="w-[10px] h-[66px] bg-[#5FEAFF] centered"></span>
        </div>
      </div> */}
    </React.Fragment>
  );
}
export default memo(Common);
