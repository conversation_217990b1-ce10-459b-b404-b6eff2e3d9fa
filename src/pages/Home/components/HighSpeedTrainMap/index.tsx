import {
  CONTAINER_HEIGHT,
  CONTAINER_SCALE_FACTOR,
  CONTAINER_WIDTH,
} from "@/utils/config";
import { Map } from "react-bmapgl";
import mapJson from "@/assets/mapStyle.json";
import React, { useEffect, useRef, useState } from "react";
import { Spin } from "antd";
import useStore from "@/store";
import { railwayData } from "@/data/railwayData";

function HighSpeedTrainMap() {
  const [loading, setLoading] = useState(true);

  const mapRef = useRef<BMapGL.Map | undefined>(undefined);

  const selectCode = useStore((state) => state.selectCode);

  useEffect(() => {
    const item = railwayData[selectCode] ?? railwayData[120000];
    if (item) {
      const center = new BMapGL.Point(item.point[0], item.point[1]);
      // @ts-expect-error
      mapRef.current?.setCenter(center, { noAnimation: true });
      // @ts-expect-error
      mapRef.current?.setZoom(item.zoom, { noAnimation: true });

      // const label = new BMapGL.Label("这里啊", {
      //   position: center,
      // });
      // mapRef.current?.addOverlay(label);
    }
    return () => {};
  }, [selectCode]);

  return (
    <React.Fragment>
      <Spin spinning={loading} className="centered" />
      <div
        className="absolute inset-x-0 inset-y-0 z-0"
        id="highSpeedTrainMap"
        style={{
          zoom: CONTAINER_SCALE_FACTOR,
          opacity: loading ? 0 : 1,
        }}
      >
        <Map
          style={{
            width: CONTAINER_WIDTH,
            height: CONTAINER_HEIGHT,
          }}
          onClick={() => {
            console.warn(mapRef.current?.getCenter(),mapRef.current?.getZoom());
          }}
          zoom={5}
          mapStyleV2={{
            styleJson: mapJson,
          }}
          ref={(value) => {
            mapRef.current = value?.map;
            // value?.map.setMapType(window.BMAP_SATELLITE_MAP);
            setTimeout(() => {
              setLoading(false);
            }, 200);
          }}
          enableScrollWheelZoom
          center={new BMapGL.Point(116.405285, 39.904989)}
        ></Map>
      </div>
    </React.Fragment>
  );
}
export default HighSpeedTrainMap;
