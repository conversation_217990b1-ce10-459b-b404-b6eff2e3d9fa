import { memo, useMemo } from "react";
import { AbilityEnum } from "@/enum";
import useStore from "@/store";
import { Segmented } from "antd";

function MenuList() {
  const selectAbilityType = useStore((state) => state.selectAbilityType);

  const options = useMemo(() => {
    return [
      {
        value: AbilityEnum.UserPriorityScheduling,
        label: "用户优先级调度",
      },
      {
        value: AbilityEnum.BusinessBandwidthGuarantee,
        label: "业务带宽保障",
      },
      {
        value: AbilityEnum.UserDynamicSpeedLimit,
        label: "用户动态限速",
      },
    ];
  }, []);

  return (
    <Segmented
      className="x-centered top-[52px] z-40 backdrop-blur-[16px]"
      options={options}
      value={selectAbilityType}
      onChange={(value) => {
        useStore.setState((draft) => {
          draft.selectAbilityType = value;
        });
      }}
    />
  );
}
export default memo(MenuList);
