import React, { memo } from "react";
import BaiduMap from "../Map";
import SubwayMap from "../SubwayMap";
import Comp3 from "../Comp3";
import HighSpeedTrainMap from "../HighSpeedTrainMap";
import useStore from "@/store";
import { AbilityEnum, SceneTypeEnum } from "@/enum";

function Center() {
  const selectAbilityType = useStore((state) => state.selectAbilityType);

  const sceneType = useStore((state) => state.sceneType);

  return (
    <React.Fragment>
      <div
        // style={{
        //   opacity: sceneType !== SceneTypeEnum.Subway ? 1 : 0,
        //   pointerEvents: sceneType !== SceneTypeEnum.Subway ? "auto" : "none",
        // }}
      >
        <BaiduMap />
      </div>
      {/* {sceneType === SceneTypeEnum.Subway && <SubwayMap />} */}
      {/* {sceneType === SceneTypeEnum.TransportationHubs && <HighSpeedTrainMap />} */}
      {/* {selectAbilityType === AbilityEnum.UserPriorityScheduling && <BaiduMap />}
      {selectAbilityType === AbilityEnum.BusinessBandwidthGuarantee && (
        <SubwayMap />
      )}
      {selectAbilityType === AbilityEnum.UserDynamicSpeedLimit && <Comp3 />} */}
    </React.Fragment>
  );
}
export default memo(Center);
