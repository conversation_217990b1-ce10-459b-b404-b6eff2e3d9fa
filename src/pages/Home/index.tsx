import Header from "./components/Header";
import Common from "./components/Common";
import MenuList from "./components/MenuList";
import ProvinceList from "./components/ProvinceList";
import DetailData from "./components/DetailData";
import SceneList from "./components/SceneList";
import Center from "./components/Center";
import React from "react";
import { Spin } from "antd";
import useStore from "@/store";

function HomePage() {
  const loading = useStore((state) => state.loading);

  return (
    <React.Fragment>
      <div
        className="w-full h-full relative bg-[#202E40] overflow-hidden"
        style={{
          opacity: loading ? 0 : 1,
        }}
      >
        <Header />
        <Center />
        <Common />
        <MenuList />
        <ProvinceList />
        <SceneList />
        <DetailData />
      </div>
      <Spin spinning={loading} className="centered" />
    </React.Fragment>
  );
}

export default HomePage;
