// src/pages/home/<USER>
export interface PolygonData {
  id: string;
  name: string;
  path: BMapGL.Point[];
  properties: any;
}


/**
 * 将坐标数组转换为 BMapGL.Point 数组
 * @param coordinates [lng, lat][] 坐标数组
 * @returns BMapGL.Point[]
 */
function convertCoordinatesToPoints(coordinates: number[][]): BMapGL.Point[] {
    return coordinates.map(([lng, lat]) => new BMapGL.Point(lng, lat));
  }
  
  /**
   * 根据区域属性获取多边形样式
   * @param properties 区域属性
   * @returns 样式对象
   */
  export function getPolygonStyle(properties: any) {
    const colors = ['#384D68', '#4A5D7A', '#5C6E8C', '#6E7F9E', '#8090B0'];
    const colorIndex = (properties.join || 0) % colors.length;
    
    return {
      strokeColor: "#FFFFFF",
      strokeWeight: 1,
      fillColor: colors[colorIndex],
      fillOpacity: 0.6
    };
  }

/**
 * 将 GeoJSON FeatureCollection 转换为 Polygon 组件所需的数据格式
 * @param featureCollection GeoJSON FeatureCollection
 * @returns PolygonData[]
 */
export function convertGeoJSONToPolygonData(featureCollection: any): PolygonData[] {
  const polygonDataList: PolygonData[] = [];

  // 遍历所有 features
  featureCollection?.features.forEach((feature: any, index: number) => {
    const { geometry, properties } = feature;
    
    // 只处理 Polygon 和 MultiPolygon 类型，跳过 Point 类型
    if (geometry.type === 'Polygon') {
      // Polygon: coordinates[0] 是外轮廓，coordinates[1+] 是内部孔洞
      const outerRing = geometry.coordinates[0];
      const path = convertCoordinatesToPoints(outerRing);
      
      polygonDataList.push({
        id: `${properties.adcode}-${index}`,
        name: properties.name,
        path,
        properties
      });
      
    } else if (geometry.type === 'MultiPolygon') {
      // MultiPolygon: 每个 polygon 都是一个独立的形状
      geometry.coordinates.forEach((polygonCoords: number[][][], polyIndex: number) => {
        // 取每个 polygon 的外轮廓 (polygonCoords[0])
        const outerRing = polygonCoords[0];
        const path = convertCoordinatesToPoints(outerRing);
        
        polygonDataList.push({
          id: `${properties.adcode}-${index}-${polyIndex}`,
          name: properties.name,
          path,
          properties
        });
      });
    }
    // 跳过 Point 类型，因为 Polygon 组件不需要
  });

  return polygonDataList;
}
