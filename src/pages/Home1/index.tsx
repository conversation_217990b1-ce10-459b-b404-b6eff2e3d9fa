import { memo, useEffect, useMemo, useRef, useState } from "react";
import { Map, Polygon } from "react-bmapgl";
import mapJson from "@/assets/map.json";

import { useEventListener } from "ahooks";
import { convertGeoJSONToPolygonData, type PolygonData } from "./utils";
import { TreeSelect } from "antd";
import { useRequest } from "ahooks";
import chinaTreeData from "@pansy/china-division";

function HomePage() {
  const mapRef = useRef<BMapGL.Map | undefined>(undefined);
  const [loading, setLoading] = useState(true);

  const [selectCode, setSelectCode] = useState(100000);

  // 底图瓦片加载完成
  useEventListener(
    "tilesloaded",
    () => {
      setLoading(false);
    },
    { target: mapRef as any }
  );

  const { data: regionData } = useRequest(
    async () => {
      try {
        const [resFull, resData] = await Promise.all([
          fetch(`/areaData/${selectCode}_full.json`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }),
          fetch(`/mapData/${selectCode}.json`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }),
        ]);

        const [areaData, mapData] = await Promise.all([
          resFull.json(),
          resData.json(),
        ]);

        return { areaData, mapData };
      } catch (error) {
        console.error("请求失败:", error);
        throw error;
      }
    },
    {
      refreshDeps: [selectCode],
    }
  );

  // 转换 GeoJSON 数据为 Polygon 组件需要的格式 拆分为多个区域渲染
  const polygonRegionData = useMemo(() => {
    return convertGeoJSONToPolygonData(regionData?.areaData);
  }, [regionData?.areaData]);

  // 省市Polygon渲染数据 渲染整体的那个 用于边框颜色加重和聚焦autoViewport
  const polygonMapData = useMemo(() => {
    return convertGeoJSONToPolygonData(regionData?.mapData);
  }, [regionData?.mapData]);

  // 获取当前城市
  useEffect(() => {
    if (!mapRef.current || !polygonMapData || polygonMapData.length === 0) {
      return;
    }

    // 是不是全国
    const isChina = selectCode === 100000;

    // 收集所有多边形的点坐标，用于设置视野
    const pointArray: BMapGL.Point[] = [];

    polygonMapData.forEach((polygonData: PolygonData) => {
      // 将每个多边形的路径点添加到数组中
      pointArray.push(...polygonData.path);
    });

    if (pointArray.length > 0) {
      // 调整视野以包含所有点
      mapRef.current.setViewport(pointArray, {
        enableAnimation: false,
        delay: 0,
        zoomFactor: isChina ? 0.3 : -0.2,
        margins: [100, 0, 0, 0],
      });
    }
  }, [polygonMapData, selectCode]);

  return (
    <div className="w-full h-full relative">
      <TreeSelect
        className="w-[500px] z-40 absolute top-[40px] left-[20px]"
        value={selectCode}
        onChange={(e) => {
          setSelectCode(Number(e));
        }}
        treeData={chinaTreeData}
      />
      <Map
        ref={(value) => {
          mapRef.current = value?.map;
          // mapRef.current?.enableAutoResize=false;
        }}
        style={{
          height: "100%",
          willChange: "transform",
          backfaceVisibility: "hidden",
          // transform: "scale(4.46512, 4.46512)",
        }}
        mapStyleV2={{
          styleJson: mapJson,
        }}
        center={new BMapGL.Point(116.405285, 39.904989)}
        // center={} // 北京市中心
        zoom={5}
        heading={0}
        tilt={0}
        // onClick={(e) => console.log(e)}
        enableScrollWheelZoom
      />

      {/* 渲染所有区域的 Polygon */}
      {!loading &&
        polygonRegionData.map((polygonData: PolygonData) => (
          <Polygon
            key={polygonData.id}
            path={polygonData.path}
            map={mapRef.current!}
            strokeColor="#FFFFFF"
            strokeWeight={1}
            strokeOpacity={0.4}
            fillColor="#384D68"
            fillOpacity={0.8}
            onClick={() => {
              console.log("点击了", polygonData.name);
            }}
          />
        ))}
      {!loading &&
        polygonMapData.map((polygonData: PolygonData) => (
          <Polygon
            key={polygonData.id}
            path={polygonData.path}
            map={mapRef.current!}
            strokeColor="#FFFFFF"
            strokeOpacity={1}
            strokeWeight={1.2}
            fillColor="#FFF"
            fillOpacity={0}
          />
        ))}
    </div>
  );
}

export default memo(HomePage);
