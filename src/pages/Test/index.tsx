import { useExternal } from "ahooks";
import { useEffect, useState } from "react";

function Test() {
  const [dimensions] = useState({ width: 800, height: 600 });

  const status = useExternal(
    "https://api.map.baidu.com/api?type=subway&v=1.0&ak=1XjLLEhZhQNUzd93EjU5nOGQ",
    {
      js: {
        async: true,
      },
      type: "js",
    }
  );

  useEffect(() => {
    if (status === "ready") {
      // 尝试传入配置选项
      const subway = new BMapSub.Subway("container", "131");
      
      console.warn(subway, "subway");
      subway.addEventListener('subwayloaded', function() {
        const drct = new BMapSub.Direction(subway);
        drct.search('西直门', '西红门');
      });
      subway.setZoom(1);

      console.warn(subway)
    }
  }, [status, dimensions]);

  return (
    <div className="w-full h-full p-4">
      <div className="flex justify-center">
        <div
          id="container"
          className="border border-gray-300 rounded"
          style={{
            width: `${dimensions.width}px`,
            height: `${dimensions.height}px`,
          }}
        />
      </div>
    </div>
  );
}
export default Test;
