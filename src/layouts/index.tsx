import ReactScaleScreen from "@/components/ReactScaleScreen";
import { PAGE_WIDTH, PAGE_HEIGHT } from "@/utils/config";
import type { ThemeConfig } from "antd";
import { ConfigProvider, theme } from "antd";
import zhCN from "antd/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { useMemo } from "react";
import { Outlet } from "umi";
dayjs.locale("zh-cn");

export default function Layout() {
  const themeConfig = useMemo(() => {
    const value: ThemeConfig = {
      token: {
        colorPrimary: "#3b64fc",
      },
      algorithm: theme.darkAlgorithm,
      components: {
        Input: {
          colorBgContainer: "rgba(255,255,255,0.10)",
          colorBorder: "transparent",
          colorPrimaryHover: "transparent",
          colorPrimary: "rgba(255,255,255,0)",
          colorBgBlur: "red",
        },
        Tree: {
          colorBgContainer: "transparent",
          titleHeight: 100,
          // colorBgSpotlight: "transparent",
        },
        Spin: {
          dotSize: 100,
        },
        Select: {
          controlHeight: 80,
          fontSize: 32,
          paddingSM: 40,
          borderRadius: 6,
          optionPadding: "20px 32px",
          colorBgContainer: "#374C6A",
          colorBorder: "#5FEAFF",
          lineWidth: 0,
          colorBgElevated: "#304765",
          optionSelectedBg: "rgba(95,234,255,.75)",
        },
        Segmented: {
          controlHeight: 128,
          fontSize: 42,
          itemSelectedBg: "#19C2DB",
          controlPaddingHorizontal: 206,
          trackBg: "rgba(255,255,255,0.08)",
          trackPadding: 0,
          itemSelectedColor: "#FFFFFF",
        },
      },
    };
    return value;
  }, []);

  return (
    <ConfigProvider
      locale={zhCN}
      wave={{
        disabled: true, // 禁用 wave
      }}
      theme={themeConfig}
    >
      <ReactScaleScreen
        className="bg-[#081525]"
        width={PAGE_WIDTH}
        height={PAGE_HEIGHT}
        style={{
          willChange: "transform",
          backfaceVisibility: "hidden",
          perspective: 1000,
        }}
      >
        <Outlet />
      </ReactScaleScreen>
    </ConfigProvider>
  );
}
