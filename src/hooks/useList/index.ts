import type { ActionType } from "@/components/ProTable";
import { useSetState } from "ahooks";
import { useCallback, useRef } from "react";

import { nanoid } from "nanoid";

interface State<T = any> {
  editKey: string; // 需要重新加载新增编辑弹框内容时，重置key
  detailKey: string; // 需要重新加载详情弹框内容时，重置key
  detailVisible: boolean; // 是否显示详情弹框
  editVisible: boolean; // 是否显示编辑弹框
  selectItem?: T; // 选中的项
}

/**
 * 为基础列表提供新增编辑详情弹框对应需要的hook
 * 返回一个对象，包含以下属性：
 * @return {string} editKey 刷新弹框的key 编辑，新增会更新
 * @return {string} detailKey 刷新弹框的key 详情会更新
 * @return {boolean} detailVisible 是否显示详情弹框
 * @return {boolean} editVisible 是否显示编辑弹框
 * @return {ActionType} actionRef 列表绑定的actionRef
 * @return {()=>void} reloadList 刷新列表方法
 * @return {()=>void} onAdd 点击新增弹框方法
 * @return {()=>void} onEdit 点击编辑弹框方法
 * @return {()=>void} onDetail 点击详情弹框方法
 * @return {()=>void} onEditClose 关闭新增和编辑弹框方法
 * @return {()=>void} onDetailClose 关闭详情弹框方法
 */
function useList<T = any>() {
  const [state, setState] = useSetState<State<T>>({
    editKey: nanoid(),
    detailKey: nanoid(),
    detailVisible: false,
    editVisible: false,
    selectItem: undefined,
  });

  const { editKey, detailKey, detailVisible, editVisible, selectItem } = state;

  const actionRef = useRef<ActionType>();

  const reloadList = useCallback(() => {
    console.warn(actionRef,'actionRefactionRef')
    return actionRef.current?.reload();
  }, []);

  const onAdd = useCallback(() => {
    setState({
      editKey: nanoid(),
      editVisible: true,
      selectItem: undefined,
    });
  }, [setState]);

  const onDetail = useCallback(
    (record: T) => {
      setState({
        detailKey: nanoid(),
        detailVisible: true,
        selectItem: record,
      });
    },
    [setState]
  );

  const onEdit = useCallback(
    (record: T) => {
      setState({
        editKey: nanoid(),
        editVisible: true,
        selectItem: record,
      });
    },
    [setState]
  );

  const onEditClose = useCallback(() => {
    setState({ editVisible: false });
  }, [setState]);

  const onDetailClose = useCallback(() => {
    setState({ detailVisible: false });
  }, [setState]);

  return {
    editKey,
    detailKey,
    detailVisible,
    editVisible,
    selectItem,
    actionRef,
    onAdd,
    onEdit,
    onEditClose,
    onDetailClose,
    onDetail,
    reloadList,
    setState,
  };
}

export default useList;
