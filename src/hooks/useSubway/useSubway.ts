import { useEffect, useRef, useCallback } from 'react';
import type { BusLineSearchOptions, SubwayHookReturn } from './types';

function useSubway(): SubwayHookReturn {
    const mapRef = useRef<BMapGL.Map | null>(null);
    const mapContainerRef = useRef<HTMLDivElement | null>(null);

    // 初始化百度地图
    useEffect(() => {
        // 创建地图容器DOM
        const mapContainer = document.createElement('div');
        mapContainer.id = 'subway-baidu-map-container';
        mapContainer.style.cssText = `
            position: absolute;
            top: -9999px;
            left: -9999px;
            width: 300px;
            height: 200px;
            visibility: hidden;
            pointer-events: none;
        `;

        // 添加到页面body中
        document.body.appendChild(mapContainer);
        mapContainerRef.current = mapContainer;

        // 初始化百度地图
        const map = new BMapGL.Map(mapContainer);
        map.centerAndZoom(new BMapGL.Point(116.405285, 39.904989), 12);
        mapRef.current = map;

        // 清理函数
        return () => {
            if (mapContainerRef.current) {
                document.body.removeChild(mapContainerRef.current);
                mapContainerRef.current = null;
            }
            mapRef.current = null;
        };
    }, []);

    // 搜索公交地铁线路
    const searchBusLine = useCallback((cityName: string, lineName: string, options?: BusLineSearchOptions) => {
        if (!mapRef.current) {
            const errorMsg = '地图未初始化完成，请稍后再试';
            console.warn(errorMsg);
            options?.onError?.(errorMsg);
            return;
        }

        try {
            const busLineSearch = new BMapGL.BusLineSearch(cityName, {
                renderOptions: options?.renderOptions || {
                    map: mapRef.current!,
                    selectFirstResult: false,
                    highlightMode: 2
                },
                onGetBusListComplete: (result: any) => {
                    if (result) {
                        options?.onGetBusListComplete?.(result);

                        // 默认获取第一个线路的详细信息（可配置）
                        if ((options?.autoGetFirstLine !== false) && result.getBusListItem) {
                            const firstLine = result.getBusListItem(0);
                            if (firstLine) {
                                busLineSearch.getBusLine(firstLine);
                            }
                        }
                    } else {
                        const errorMsg = `未找到线路: ${lineName}`;
                        console.warn(errorMsg);
                        options?.onError?.(errorMsg);
                    }
                },
                onGetBusLineComplete: (result: any) => {
                    debugger
                    if (result) {
                        options?.onGetBusLineComplete?.(result);
                    } else {
                        const errorMsg = '获取线路详情失败';
                        console.warn(errorMsg);
                        options?.onError?.(errorMsg);
                    }
                }
            });

            // 开始搜索
            busLineSearch.getBusList(lineName);

            return busLineSearch;
        } catch (error) {
            const errorMsg = `搜索线路时发生错误: ${error}`;
            console.error(errorMsg);
            options?.onError?.(errorMsg);
        }
    }, []);

    // 获取地图实例（如果需要更多自定义操作）
    const getMapInstance = useCallback(() => {
        return mapRef.current;
    }, []);

    // 检查地图是否已初始化
    const isMapReady = useCallback(() => {
        return mapRef.current !== null;
    }, []);

    return {
        searchBusLine,
        getMapInstance,
        isMapReady
    };
}

export default useSubway;
