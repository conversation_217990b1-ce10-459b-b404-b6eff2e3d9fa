// 百度地图公交查询相关类型定义

export interface BusLineSearchOptions {
  /** 获取线路列表完成回调 */
  onGetBusListComplete?: (result: any) => void;
  /** 获取线路详情完成回调 */
  onGetBusLineComplete?: (result: any) => void;
  /** 错误回调 */
  onError?: (error: string) => void;
  /** 百度地图渲染选项 */
  renderOptions?: any;
  /** 是否自动获取第一条线路详情，默认为 true */
  autoGetFirstLine?: boolean;
}

export interface SubwayHookReturn {
  /** 搜索公交地铁线路 */
  searchBusLine: (cityName: string, lineName: string, options?: BusLineSearchOptions) => BMapGL.BusLineSearch | undefined;
  /** 获取地图实例 */
  getMapInstance: () => BMapGL.Map | null;
  /** 检查地图是否已初始化 */
  isMapReady: () => boolean;
}

export interface StationInfo {
  name: string;
  position?: BMapGL.Point;
  [key: string]: any;
}

export interface LineInfo {
  name: string;
  stations: StationInfo[];
  path: BMapGL.Point[];
  [key: string]: any;
}
